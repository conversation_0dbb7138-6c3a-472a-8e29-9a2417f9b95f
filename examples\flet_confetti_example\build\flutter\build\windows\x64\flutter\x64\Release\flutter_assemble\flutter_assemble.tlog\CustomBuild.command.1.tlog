^C:\USERS\<USER>\DESKTOP\TEST\EXAMPLES\FLET_CONFETTI_EXAMPLE\BUILD\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\21764170A186BD814FCE857824B29295\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\Users\<USER>\Documents\Flutter\flutter PROJECT_DIR=C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter FLUTTER_ROOT=C:\Users\<USER>\Documents\Flutter\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter FLUTTER_TARGET=lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter\.dart_tool\package_config.json C:/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\TEST\EXAMPLES\FLET_CONFETTI_EXAMPLE\BUILD\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\CE7A9CC3687EC2FCE176D42AB425BD65\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\TEST\EXAMPLES\FLET_CONFETTI_EXAMPLE\BUILD\FLUTTER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/windows -BC:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
