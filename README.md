# flet-confetti
🎊 **FletConfetti** - A powerful confetti animation control for Flet applications

## ✨ Features

- 🎨 **25+ Predefined Color Themes** (Christmas, Halloween, Birthday, Neon, etc.)
- 📏 **Size Objects** for easy particle dimension control
- 🎯 **Directional & Explosive** blast patterns
- 🔥 **20+ Particle Shapes** (hearts, stars, butterflies, etc.)
- ⚡ **High Performance** with customizable physics
- 🎮 **Easy Integration** with Flet apps

## 🚀 Quick Start

```python
import flet as ft
from flet_confetti import FletConfetti, ConfettiTheme, ParticleShape
from flet.core.size import Size

def main(page: ft.Page):
    # Create confetti with theme and size
    confetti = FletConfetti(
        theme=ConfettiTheme.BIRTHDAY,  # 🎂 Predefined color theme
        particle_shape=ParticleShape.HEART,
        minimum_size=Size(15, 10),     # 📏 Easy size control
        maximum_size=Size(25, 18),
        number_of_particles=20,
    )

    page.add(
        ft.Column([
            ft.ElevatedButton("🎉 Celebrate!", on_click=lambda _: confetti.play()),
            confetti,
        ])
    )

ft.app(main)
```

## 🎨 Color Themes

Choose from 25+ beautiful predefined themes:

### 🎄 Festive Themes
- `ConfettiTheme.CHRISTMAS` - Red, green, gold
- `ConfettiTheme.HALLOWEEN` - Orange, black, purple
- `ConfettiTheme.VALENTINE` - Pink, red, white
- `ConfettiTheme.EASTER` - Pastel colors
- `ConfettiTheme.NEW_YEAR` - Gold, silver, black

### ✨ Metallic Themes
- `ConfettiTheme.GOLD` - Golden shades
- `ConfettiTheme.SILVER` - Silver tones
- `ConfettiTheme.BRONZE` - Bronze colors
- `ConfettiTheme.RAINBOW_METALLIC` - Metallic rainbow

### 🌈 Nature & Fun
- `ConfettiTheme.OCEAN` - Blues and teals
- `ConfettiTheme.SUNSET` - Orange, pink, purple
- `ConfettiTheme.FOREST` - Green nature tones
- `ConfettiTheme.NEON` - Bright neon colors
- `ConfettiTheme.PASTEL` - Soft pastel colors

### 🎉 Party Themes
- `ConfettiTheme.BIRTHDAY` - Rainbow colors
- `ConfettiTheme.GRADUATION` - School colors
- `ConfettiTheme.WEDDING` - Elegant whites and golds
- `ConfettiTheme.PRIDE` - Rainbow pride colors

## 📏 Size Control

Use `Size` objects for intuitive particle sizing:

```python
from flet.core.size import Size

# Tiny particles
confetti = FletConfetti(
    minimum_size=Size(5, 3),
    maximum_size=Size(8, 5),
)

# Large particles
confetti = FletConfetti(
    minimum_size=Size(30, 20),
    maximum_size=Size(45, 30),
)
```

## 🎯 Advanced Usage

```python
import math
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape, ConfettiTheme

# Directional confetti
confetti = FletConfetti(
    theme=ConfettiTheme.CHRISTMAS,
    blast_directionality=BlastDirectionality.DIRECTIONAL,
    blast_direction=math.pi / 2,  # Downward
    particle_shape=ParticleShape.SNOWFLAKE,
    minimum_size=Size(12, 8),
    maximum_size=Size(20, 14),
    gravity=0.3,
    max_blast_force=50,
    duration_seconds=5,
)

# Custom colors (overrides theme)
confetti = FletConfetti(
    colors=["#FF0000", "#00FF00", "#0000FF"],  # Custom colors
    theme=ConfettiTheme.GOLD,  # Will be ignored
    particle_shape=ParticleShape.STAR,
)
```

## 🎮 Available Shapes

20+ particle shapes available:
- Basic: `CIRCLE`, `SQUARE`, `TRIANGLE`, `DIAMOND`
- Stars: `STAR`, `STAR_4`, `STAR_6`, `STAR_8`
- Fun: `HEART`, `FLOWER`, `BUTTERFLY`, `LIGHTNING`
- Symbols: `CROSS`, `ARROW`, `SNOWFLAKE`, `CROWN`

## 📦 Installation

Add dependency to `pyproject.toml` of your Flet app:

```toml
dependencies = [
  "flet-confetti @ git+https://github.com/YourAccount/flet-confetti",
  "flet>=0.28.3",
]
```

Build your app:
```bash
flet build macos -v
```

## 🎯 Examples

Check out the example applications:
- `examples/flet_confetti_example/src/main.py` - Full interactive demo
- `examples/flet_confetti_example/src/theme_demo.py` - Theme showcase
- `examples/flet_confetti_example/src/size_demo.py` - Size demonstration

## 📚 API Reference

### FletConfetti Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `theme` | `ConfettiTheme` | Predefined color theme |
| `colors` | `List[ColorValue]` | Custom colors (overrides theme) |
| `minimum_size` | `Size` | Minimum particle size |
| `maximum_size` | `Size` | Maximum particle size |
| `particle_shape` | `ParticleShape` | Particle shape |
| `blast_directionality` | `BlastDirectionality` | Direction pattern |
| `number_of_particles` | `int` | Particle count |
| `duration_seconds` | `int` | Animation duration |

### Methods

- `play()` - Start confetti animation
- `stop(clear_all_particles=False)` - Stop animation
- `reload()` - Reset controller state

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License.
