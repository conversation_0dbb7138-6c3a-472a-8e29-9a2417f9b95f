import flet as ft
import math
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape, ConfettiTheme
from flet.core.size import Size


def main(page: ft.Page):
    page.title = "Flet Confetti - Size Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create confetti with different sizes
    confetti_tiny = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.GOLD,
        particle_shape=ParticleShape.STAR,
        duration_seconds=4,
        number_of_particles=30,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.2,
        minimum_size=Size(5, 3),    # Very small particles
        maximum_size=Size(8, 5),
    )

    confetti_small = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.OCEAN,
        particle_shape=ParticleShape.CIRCLE,
        duration_seconds=4,
        number_of_particles=25,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.2,
        minimum_size=Size(10, 8),   # Small particles
        maximum_size=Size(15, 12),
    )

    confetti_medium = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.FOREST,
        particle_shape=ParticleShape.HEART,
        duration_seconds=4,
        number_of_particles=20,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.2,
        minimum_size=Size(18, 12),  # Medium particles
        maximum_size=Size(25, 18),
    )

    confetti_large = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.SUNSET,
        particle_shape=ParticleShape.FLOWER,
        duration_seconds=4,
        number_of_particles=15,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.2,
        minimum_size=Size(30, 20),  # Large particles
        maximum_size=Size(40, 30),
    )

    confetti_huge = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.PRIDE,
        particle_shape=ParticleShape.BUTTERFLY,
        duration_seconds=4,
        number_of_particles=10,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.2,
        minimum_size=Size(45, 35),  # Huge particles
        maximum_size=Size(60, 45),
    )

    # Status text
    status_text = ft.Text(
        "📏 Choose a size to see different particle dimensions! 📏",
        size=18,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )

    # Current size display
    current_size_text = ft.Text(
        "Current Size: None",
        size=14,
        color=ft.Colors.WHITE70,
        text_align=ft.TextAlign.CENTER,
    )

    def play_size_confetti(confetti: FletConfetti, size_name: str, size_desc: str):
        """Play confetti for a specific size"""
        try:
            confetti.play()
            status_text.value = f"🎊 {size_name} confetti!"
            current_size_text.value = f"Current Size: {size_desc}"
            min_size = confetti.minimum_size
            max_size = confetti.maximum_size
            print(f"Playing {size_name} confetti - Size range: {min_size.width}x{min_size.height} to {max_size.width}x{max_size.height}")
        except Exception as e:
            status_text.value = f"❌ Error: {e}"
        page.update()

    def stop_all_confetti(_):
        """Stop all confetti"""
        try:
            confetti_tiny.stop(clear_all_particles=True)
            confetti_small.stop(clear_all_particles=True)
            confetti_medium.stop(clear_all_particles=True)
            confetti_large.stop(clear_all_particles=True)
            confetti_huge.stop(clear_all_particles=True)
            status_text.value = "🛑 All confetti stopped!"
            current_size_text.value = "Current Size: None"
        except Exception as e:
            status_text.value = f"❌ Stop error: {e}"
        page.update()

    # Create layout
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Container(
                            content=ft.Column([
                                ft.Text(
                                    "📏 CONFETTI SIZE SHOWCASE 📏",
                                    size=24,
                                    color=ft.Colors.WHITE,
                                    weight=ft.FontWeight.BOLD,
                                    text_align=ft.TextAlign.CENTER,
                                ),
                                status_text,
                                current_size_text,
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.Text(
                                    "🔍 Particle Sizes (using Size objects)",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                
                                ft.Row([
                                    ft.ElevatedButton(
                                        "🔸 Tiny\n5x3 to 8x5",
                                        on_click=lambda e: play_size_confetti(confetti_tiny, "Tiny", "5x3 to 8x5"),
                                        bgcolor=ft.Colors.YELLOW_600,
                                        color=ft.Colors.BLACK,
                                        style=ft.ButtonStyle(
                                            padding=ft.Padding(15, 15, 15, 15),
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                        ),
                                    ),
                                    ft.ElevatedButton(
                                        "🔹 Small\n10x8 to 15x12",
                                        on_click=lambda e: play_size_confetti(confetti_small, "Small", "10x8 to 15x12"),
                                        bgcolor=ft.Colors.CYAN_600,
                                        color=ft.Colors.WHITE,
                                        style=ft.ButtonStyle(
                                            padding=ft.Padding(15, 15, 15, 15),
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                        ),
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Row([
                                    ft.ElevatedButton(
                                        "🔶 Medium\n18x12 to 25x18",
                                        on_click=lambda e: play_size_confetti(confetti_medium, "Medium", "18x12 to 25x18"),
                                        bgcolor=ft.Colors.GREEN_600,
                                        color=ft.Colors.WHITE,
                                        style=ft.ButtonStyle(
                                            padding=ft.Padding(15, 15, 15, 15),
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                        ),
                                    ),
                                    ft.ElevatedButton(
                                        "🔷 Large\n30x20 to 40x30",
                                        on_click=lambda e: play_size_confetti(confetti_large, "Large", "30x20 to 40x30"),
                                        bgcolor=ft.Colors.DEEP_ORANGE_600,
                                        color=ft.Colors.WHITE,
                                        style=ft.ButtonStyle(
                                            padding=ft.Padding(15, 15, 15, 15),
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                        ),
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Row([
                                    ft.ElevatedButton(
                                        "🔴 Huge\n45x35 to 60x45",
                                        on_click=lambda e: play_size_confetti(confetti_huge, "Huge", "45x35 to 60x45"),
                                        bgcolor=ft.Colors.PURPLE_600,
                                        color=ft.Colors.WHITE,
                                        style=ft.ButtonStyle(
                                            padding=ft.Padding(15, 15, 15, 15),
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                        ),
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.ElevatedButton(
                                    "🛑 STOP ALL",
                                    on_click=stop_all_confetti,
                                    bgcolor=ft.Colors.RED_600,
                                    color=ft.Colors.WHITE,
                                    style=ft.ButtonStyle(
                                        padding=ft.Padding(20, 12, 20, 12),
                                        shape=ft.RoundedRectangleBorder(radius=15),
                                    ),
                                ),
                                
                                ft.Text(
                                    "💡 Using Size(width, height) objects!\n"
                                    "📏 Each size uses different particle counts\n"
                                    "🎨 Different themes for visual distinction\n"
                                    "⚡ Smaller particles = more particles",
                                    size=12,
                                    color=ft.Colors.WHITE70,
                                    text_align=ft.TextAlign.CENTER,
                                    italic=True,
                                ),
                            ], 
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=15),
                            bgcolor=ft.Colors.with_opacity(0.9, ft.Colors.BLACK),
                            border_radius=20,
                            padding=30,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    expand=True,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                
                # All confetti widgets (invisible but positioned for effects)
                confetti_tiny,
                confetti_small,
                confetti_medium,
                confetti_large,
                confetti_huge,
            ],
        )
    )


if __name__ == "__main__":
    ft.app(main)
