import flet as ft
import math
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape, ConfettiTheme
from flet.core.size import Size


def main(page: ft.Page):
    page.title = "Flet Confetti - Theme Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create confetti with different themes
    confetti_widgets = {}
    
    # Define themes to showcase
    showcase_themes = [
        (ConfettiTheme.CHRISTMAS, "🎄 Christmas", ft.Colors.RED_400),
        (ConfettiTheme.HALLOWEEN, "🎃 Halloween", ft.Colors.ORANGE_600),
        (ConfettiTheme.VALENTINE, "💝 Valentine", ft.Colors.PINK_400),
        (ConfettiTheme.BIRTHDAY, "🎂 Birthday", ft.Colors.BLUE_400),
        (ConfettiTheme.GOLD, "🏆 Gold", ft.Colors.YELLOW_600),
        (ConfettiTheme.NEON, "⚡ Neon", ft.Colors.PURPLE_400),
        (ConfettiTheme.OCEAN, "🌊 Ocean", ft.Colors.CYAN_400),
        (ConfettiTheme.SUNSET, "🌅 Sunset", ft.Colors.DEEP_ORANGE_400),
        (ConfettiTheme.FOREST, "🌲 Forest", ft.Colors.GREEN_400),
        (ConfettiTheme.PRIDE, "🏳️‍🌈 Pride", ft.Colors.INDIGO_400),
    ]

    # Create confetti widgets for each theme
    for theme, name, color in showcase_themes:
        confetti = FletConfetti(
            blast_directionality=BlastDirectionality.EXPLOSIVE,
            theme=theme,
            particle_shape=ParticleShape.HEART,
            duration_seconds=4,
            number_of_particles=20,
            should_loop=False,
            max_blast_force=50,
            min_blast_force=25,
            gravity=0.3,
            minimum_size=Size(12, 8),
            maximum_size=Size(20, 14),
        )
        confetti_widgets[theme] = confetti

    # Status text
    status_text = ft.Text(
        "🎨 Choose a theme to see confetti colors! 🎨",
        size=18,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )

    # Current theme display
    current_theme_text = ft.Text(
        "Current Theme: None",
        size=14,
        color=ft.Colors.WHITE70,
        text_align=ft.TextAlign.CENTER,
    )

    def play_theme_confetti(theme: ConfettiTheme, theme_name: str):
        """Play confetti for a specific theme"""
        try:
            confetti = confetti_widgets[theme]
            confetti.play()
            status_text.value = f"🎊 {theme_name} confetti!"
            current_theme_text.value = f"Current Theme: {theme.value}"
            print(f"Playing {theme_name} confetti with colors: {confetti.colors}")
        except Exception as e:
            status_text.value = f"❌ Error: {e}"
        page.update()

    def stop_all_confetti(_):
        """Stop all confetti"""
        try:
            for confetti in confetti_widgets.values():
                confetti.stop(clear_all_particles=True)
            status_text.value = "🛑 All confetti stopped!"
            current_theme_text.value = "Current Theme: None"
        except Exception as e:
            status_text.value = f"❌ Stop error: {e}"
        page.update()

    # Create theme buttons
    theme_buttons = []
    for i, (theme, name, color) in enumerate(showcase_themes):
        button = ft.ElevatedButton(
            name,
            on_click=lambda e, t=theme, n=name: play_theme_confetti(t, n),
            bgcolor=color,
            color=ft.Colors.WHITE,
            style=ft.ButtonStyle(
                padding=ft.Padding(15, 10, 15, 10),
                shape=ft.RoundedRectangleBorder(radius=10),
            ),
        )
        theme_buttons.append(button)

    # Create layout
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Container(
                            content=ft.Column([
                                ft.Text(
                                    "🎨 CONFETTI THEME SHOWCASE 🎨",
                                    size=24,
                                    color=ft.Colors.WHITE,
                                    weight=ft.FontWeight.BOLD,
                                    text_align=ft.TextAlign.CENTER,
                                ),
                                status_text,
                                current_theme_text,
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.Text(
                                    "🎯 Festive Themes",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Row([
                                    theme_buttons[0],  # Christmas
                                    theme_buttons[1],  # Halloween
                                    theme_buttons[2],  # Valentine
                                    theme_buttons[3],  # Birthday
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                                
                                ft.Text(
                                    "✨ Metallic & Special",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Row([
                                    theme_buttons[4],  # Gold
                                    theme_buttons[5],  # Neon
                                    theme_buttons[9],  # Pride
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                                
                                ft.Text(
                                    "🌈 Nature & Colors",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Row([
                                    theme_buttons[6],  # Ocean
                                    theme_buttons[7],  # Sunset
                                    theme_buttons[8],  # Forest
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                                
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.ElevatedButton(
                                    "🛑 STOP ALL",
                                    on_click=stop_all_confetti,
                                    bgcolor=ft.Colors.RED_600,
                                    color=ft.Colors.WHITE,
                                    style=ft.ButtonStyle(
                                        padding=ft.Padding(20, 12, 20, 12),
                                        shape=ft.RoundedRectangleBorder(radius=15),
                                    ),
                                ),
                                
                                ft.Text(
                                    "💡 Each theme has carefully curated colors!\n"
                                    "🎨 Themes override colors parameter\n"
                                    "📏 Using Size objects for particle dimensions",
                                    size=12,
                                    color=ft.Colors.WHITE70,
                                    text_align=ft.TextAlign.CENTER,
                                    italic=True,
                                ),
                            ], 
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=15),
                            bgcolor=ft.Colors.with_opacity(0.9, ft.Colors.BLACK),
                            border_radius=20,
                            padding=30,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    expand=True,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                
                # All confetti widgets (invisible but positioned for effects)
                *confetti_widgets.values(),
            ],
        )
    )


if __name__ == "__main__":
    ft.app(main)
