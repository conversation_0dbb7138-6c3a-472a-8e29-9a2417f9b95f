import flet as ft
from flet_confetti import FletConfetti, ConfettiTheme, ParticleShape
from flet.core.size import Size


def main(page: ft.Page):
    page.title = "Simple Confetti Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create a simple confetti with theme
    confetti = FletConfetti(
        theme=ConfettiTheme.BIRTHDAY,
        particle_shape=ParticleShape.HEART,
        minimum_size=<PERSON>ze(15, 10),
        maximum_size=Size(25, 18),
        number_of_particles=20,
        duration_seconds=5,
    )

    # Status text
    status_text = ft.Text(
        "🎊 Simple Confetti Test 🎊",
        size=20,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )

    def play_confetti(_):
        try:
            result = confetti.play()
            status_text.value = f"✅ Confetti played! Result: {result}"
            print(f"Play result: {result}")
        except Exception as e:
            status_text.value = f"❌ Error: {e}"
            print(f"Error: {e}")
        page.update()

    def stop_confetti(_):
        try:
            result = confetti.stop(clear_all_particles=True)
            status_text.value = f"🛑 Confetti stopped! Result: {result}"
            print(f"Stop result: {result}")
        except Exception as e:
            status_text.value = f"❌ Error: {e}"
            print(f"Error: {e}")
        page.update()

    def test_theme_change(_):
        try:
            # Change to a different theme
            confetti.theme = ConfettiTheme.CHRISTMAS
            status_text.value = "🎄 Changed to Christmas theme!"
            print(f"Theme changed to: {confetti.theme}")
            print(f"Colors: {confetti.colors}")
        except Exception as e:
            status_text.value = f"❌ Theme error: {e}"
            print(f"Theme error: {e}")
        page.update()

    def test_custom_colors(_):
        try:
            # Use custom colors (should override theme)
            confetti.colors = ["#FF0000", "#00FF00", "#0000FF"]
            status_text.value = "🌈 Changed to custom colors!"
            print(f"Colors: {confetti.colors}")
            print(f"Theme: {confetti.theme}")
        except Exception as e:
            status_text.value = f"❌ Colors error: {e}"
            print(f"Colors error: {e}")
        page.update()

    def test_size_change(_):
        try:
            # Change size
            confetti.minimum_size = Size(20, 15)
            confetti.maximum_size = Size(35, 25)
            status_text.value = "📏 Changed particle size!"
            print(f"Min size: {confetti.minimum_size}")
            print(f"Max size: {confetti.maximum_size}")
        except Exception as e:
            status_text.value = f"❌ Size error: {e}"
            print(f"Size error: {e}")
        page.update()

    # Create layout
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Container(
                            content=ft.Column([
                                status_text,
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.Text(
                                    "🎮 Basic Controls",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Row([
                                    ft.ElevatedButton(
                                        "🎉 PLAY",
                                        on_click=play_confetti,
                                        bgcolor=ft.Colors.GREEN_600,
                                        color=ft.Colors.WHITE,
                                    ),
                                    ft.ElevatedButton(
                                        "🛑 STOP",
                                        on_click=stop_confetti,
                                        bgcolor=ft.Colors.RED_600,
                                        color=ft.Colors.WHITE,
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Divider(color=ft.Colors.WHITE24, height=20),
                                
                                ft.Text(
                                    "🧪 Property Tests",
                                    size=16,
                                    color=ft.Colors.YELLOW,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Row([
                                    ft.ElevatedButton(
                                        "🎄 Theme",
                                        on_click=test_theme_change,
                                        bgcolor=ft.Colors.BLUE_600,
                                        color=ft.Colors.WHITE,
                                    ),
                                    ft.ElevatedButton(
                                        "🌈 Colors",
                                        on_click=test_custom_colors,
                                        bgcolor=ft.Colors.PURPLE_600,
                                        color=ft.Colors.WHITE,
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Row([
                                    ft.ElevatedButton(
                                        "📏 Size",
                                        on_click=test_size_change,
                                        bgcolor=ft.Colors.ORANGE_600,
                                        color=ft.Colors.WHITE,
                                    ),
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                
                                ft.Text(
                                    "💡 Check console for debug info",
                                    size=12,
                                    color=ft.Colors.WHITE70,
                                    text_align=ft.TextAlign.CENTER,
                                    italic=True,
                                ),
                            ], 
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=15),
                            bgcolor=ft.Colors.with_opacity(0.9, ft.Colors.BLACK),
                            border_radius=20,
                            padding=30,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    expand=True,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                
                # Confetti widget
                confetti,
            ],
        )
    )


if __name__ == "__main__":
    ft.app(main)
