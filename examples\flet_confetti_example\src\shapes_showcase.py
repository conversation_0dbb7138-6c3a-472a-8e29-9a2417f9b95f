import flet as ft
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape


def main(page: ft.Page):
    page.title = "Flet Confetti - Shapes Showcase"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900
    
    # Create confetti widget
    confetti = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        colors=["red", "blue", "green", "orange", "purple"],
        particle_shape=ParticleShape.STAR,
        duration_seconds=3,
        number_of_particles=30,
        should_loop=False,
        width=400,
        height=300,
    )
    
    # Status text
    status_text = ft.Text(
        "Choose a shape and click Play!",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
    )
    
    # Current shape display
    current_shape_text = ft.Text(
        f"Current Shape: {ParticleShape.STAR.value}",
        size=14,
        color=ft.Colors.YELLOW,
        text_align=ft.TextAlign.CENTER,
    )
    
    def play_confetti():
        try:
            confetti.reload()  # Ensure fresh controller
            result = confetti.play()
            status_text.value = f"✨ Playing {confetti.particle_shape.value} confetti!"
            page.update()
        except Exception as e:
            status_text.value = f"❌ Error: {e}"
            page.update()
    
    def change_shape(shape: ParticleShape):
        confetti.particle_shape = shape
        current_shape_text.value = f"Current Shape: {shape.value}"
        status_text.value = f"Shape changed to: {shape.value}"
        page.update()
    
    # Create shape buttons organized by category
    def create_shape_button(shape: ParticleShape, emoji: str = "🔸"):
        return ft.ElevatedButton(
            f"{emoji} {shape.value.replace('_', ' ').title()}",
            on_click=lambda _: change_shape(shape),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_GREY_800,
                color=ft.Colors.WHITE,
                padding=ft.Padding(10, 5, 10, 5),
            ),
        )
    
    # Basic shapes
    basic_shapes = ft.Column([
        ft.Text("🔷 Basic Shapes", size=16, color=ft.Colors.CYAN, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.RECTANGLE, "⬜"),
            create_shape_button(ParticleShape.CIRCLE, "⭕"),
            create_shape_button(ParticleShape.SQUARE, "🟦"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Geometric shapes
    geometric_shapes = ft.Column([
        ft.Text("📐 Geometric Shapes", size=16, color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.TRIANGLE, "🔺"),
            create_shape_button(ParticleShape.DIAMOND, "💎"),
            create_shape_button(ParticleShape.HEXAGON, "⬡"),
            create_shape_button(ParticleShape.PENTAGON, "⬟"),
            create_shape_button(ParticleShape.OCTAGON, "🛑"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Star variations
    star_shapes = ft.Column([
        ft.Text("⭐ Star Variations", size=16, color=ft.Colors.YELLOW, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.STAR, "⭐"),
            create_shape_button(ParticleShape.STAR_4, "✦"),
            create_shape_button(ParticleShape.STAR_6, "✡"),
            create_shape_button(ParticleShape.STAR_8, "✨"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Fun shapes
    fun_shapes = ft.Column([
        ft.Text("🎨 Fun Shapes", size=16, color=ft.Colors.PINK, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.HEART, "❤️"),
            create_shape_button(ParticleShape.FLOWER, "🌸"),
            create_shape_button(ParticleShape.LEAF, "🍃"),
            create_shape_button(ParticleShape.BUTTERFLY, "🦋"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Symbol shapes
    symbol_shapes = ft.Column([
        ft.Text("🔣 Symbols", size=16, color=ft.Colors.ORANGE, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.CROSS, "✝️"),
            create_shape_button(ParticleShape.PLUS, "➕"),
            create_shape_button(ParticleShape.ARROW, "⬆️"),
            create_shape_button(ParticleShape.LIGHTNING, "⚡"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Special shapes
    special_shapes = ft.Column([
        ft.Text("👑 Special Shapes", size=16, color=ft.Colors.PURPLE, weight=ft.FontWeight.BOLD),
        ft.Row([
            create_shape_button(ParticleShape.SKULL, "💀"),
            create_shape_button(ParticleShape.CROWN, "👑"),
            create_shape_button(ParticleShape.SNOWFLAKE, "❄️"),
            create_shape_button(ParticleShape.MUSIC_NOTE, "🎵"),
        ], wrap=True, spacing=5),
    ], spacing=10)
    
    # Control buttons
    control_buttons = ft.Row([
        ft.ElevatedButton(
            "🎉 PLAY CONFETTI",
            on_click=lambda _: play_confetti(),
            bgcolor=ft.Colors.GREEN,
            color=ft.Colors.WHITE,
            style=ft.ButtonStyle(
                padding=ft.Padding(20, 10, 20, 10),
            ),
        ),
        ft.ElevatedButton(
            "⏹️ Stop",
            on_click=lambda _: confetti.stop(),
            bgcolor=ft.Colors.RED,
            color=ft.Colors.WHITE,
        ),
    ], alignment=ft.MainAxisAlignment.CENTER, spacing=20)
    
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.START
    
    page.add(
        ft.Stack([
            ft.Column([
                ft.Text(
                    "🎊 Confetti Shapes Showcase 🎊",
                    size=24,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                ),
                current_shape_text,
                status_text,
                ft.Divider(color=ft.Colors.WHITE24),
                
                # Shape categories
                basic_shapes,
                geometric_shapes,
                star_shapes,
                fun_shapes,
                symbol_shapes,
                special_shapes,
                
                ft.Divider(color=ft.Colors.WHITE24),
                control_buttons,
                
                ft.Text(
                    "💡 Tip: Each shape is rendered using custom Path definitions!",
                    size=12,
                    color=ft.Colors.WHITE70,
                    text_align=ft.TextAlign.CENTER,
                    italic=True,
                ),
            ], 
            spacing=15,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
            ),
            confetti,
        ]),
    )


if __name__ == "__main__":
    ft.app(main)
