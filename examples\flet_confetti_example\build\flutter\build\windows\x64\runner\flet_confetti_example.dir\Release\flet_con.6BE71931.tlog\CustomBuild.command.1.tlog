^C:\USERS\<USER>\DESKTOP\TEST\EXAMPLES\FLET_CONFETTI_EXAMPLE\BUILD\FLUTTER\WINDOWS\RUNNER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/windows -BC:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/test/examples/flet_confetti_example/build/flutter/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
