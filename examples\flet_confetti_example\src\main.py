import flet as ft
import math
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape


def main(page: ft.Page):
    page.title = "Flet Confetti Example - Enhanced"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create confetti with enhanced configuration using new shape system
    confetti = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        colors=[ft.Colors.PRIMARY, ft.Colors.SECONDARY, ft.Colors.TERTIARY],
        particle_shape=ParticleShape.HEART,  # Using new shape system!
        duration_seconds=5,  # 5 second duration
        number_of_particles=10,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.4,
    )

    # Status text to show results
    status_text = ft.Text(
        "Ready to blast confetti!",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
    )

    def on_animation_end(_):
        status_text.value = "Animation ended!"
        page.update()

    def on_play_click(_):
        try:
            # Check state before play
            state_before = confetti.get_controller_state()
            print(f"=== PLAY: State before: {state_before}")
            import random

            # Randomize colors
            confetti.colors = [ft.Colors.random() for _ in range(3)]

            # Randomize shape from available shapes
            shapes = [
                ParticleShape.HEART,
                ParticleShape.STAR,
                ParticleShape.CIRCLE,
                ParticleShape.TRIANGLE,
                ParticleShape.DIAMOND,
                ParticleShape.FLOWER,
                ParticleShape.BUTTERFLY,
                ParticleShape.LIGHTNING,
                ParticleShape.SNOWFLAKE,
                ParticleShape.CROSS,
                ParticleShape.ARROW,
                ParticleShape.HEXAGON,
            ]
            confetti.particle_shape = random.choice(shapes)
            print(f"Using shape: {confetti.particle_shape.value}")

            # Randomize other properties
            if random.random() < 0.5:
                confetti.blast_directionality = BlastDirectionality.DIRECTIONAL
            else:
                confetti.blast_directionality = BlastDirectionality.EXPLOSIVE
            confetti.blast_direction = random.uniform(0, 2 * math.pi)
            confetti.max_blast_force = random.uniform(20, 80)
            confetti.min_blast_force = random.uniform(10, 40)
            confetti.emission_frequency = random.uniform(0.01, 0.09)
            confetti.gravity = random.uniform(-0.6, 0.6)
            confetti.number_of_particles = random.randint(5, 15)
            confetti.reload()
            result = confetti.play()
            status_text.value = f"✅ Play result: {result}"
            print(f"Play called, result: {result}")

            # Check state after play
            state_after = confetti.get_controller_state()
            print(f"=== PLAY: State after: {state_after}")
        except Exception as e:
            status_text.value = f"❌ Play error: {e}"
            print(f"Play error: {e}")
        page.update()

    def on_stop_click(_):
        try:
            result = confetti.stop(clear_all_particles=False)
            status_text.value = f"⏹️ Stop result: {result}"
            print(f"Stop called, result: {result}")
        except Exception as e:
            status_text.value = f"❌ Stop error: {e}"
            print(f"Stop error: {e}")
        page.update()

    def on_stop_clear_click(_):
        try:
            result = confetti.stop(clear_all_particles=True)
            status_text.value = f"🧹 Stop+Clear result: {result}"
            print(f"Stop with clear called, result: {result}")
        except Exception as e:
            status_text.value = f"❌ Stop+Clear error: {e}"
            print(f"Stop+Clear error: {e}")
        page.update()

    def on_reload_click(_):
        try:
            print("=== RELOAD SEQUENCE START ===")
            # Check state before reload
            state_before = confetti.get_controller_state()
            print(f"State before reload: {state_before}")

            # Perform reload
            result = confetti.reload()
            print(f"Reload called, result: {result}")

            # Check state after reload
            state_after = confetti.get_controller_state()
            print(f"State after reload: {state_after}")

            status_text.value = f"🔄 Reload: {result} | State: {state_after}"
            print("=== RELOAD SEQUENCE END ===")
        except Exception as e:
            status_text.value = f"❌ Reload error: {e}"
            print(f"Reload error: {e}")
        page.update()

    def on_check_state_click(_):
        try:
            state = confetti.get_controller_state()
            status_text.value = f"🔍 Controller state: {state}"
            print(f"Controller state: {state}")
        except Exception as e:
            status_text.value = f"❌ State check error: {e}"
            print(f"State check error: {e}")
        page.update()

    # Set event handler
    confetti.on_animation_end = on_animation_end

    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER

    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        status_text,
                        ft.Row(
                            [
                                ft.ElevatedButton(
                                    "🎉 Play",
                                    on_click=on_play_click,
                                    bgcolor=ft.Colors.GREEN,
                                    color=ft.Colors.WHITE,
                                ),
                                ft.ElevatedButton(
                                    "⏹️ Stop",
                                    on_click=on_stop_click,
                                    bgcolor=ft.Colors.ORANGE,
                                    color=ft.Colors.WHITE,
                                ),
                                ft.ElevatedButton(
                                    "🧹 Stop & Clear",
                                    on_click=on_stop_clear_click,
                                    bgcolor=ft.Colors.RED,
                                    color=ft.Colors.WHITE,
                                ),
                                ft.ElevatedButton(
                                    "🔄 Reload",
                                    on_click=on_reload_click,
                                    bgcolor=ft.Colors.BLUE,
                                    color=ft.Colors.WHITE,
                                ),
                                ft.ElevatedButton(
                                    "🔍 Check State",
                                    on_click=on_check_state_click,
                                    bgcolor=ft.Colors.PURPLE,
                                    color=ft.Colors.WHITE,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                            spacing=10,
                        ),
                    ],
                    expand=True,
                ),
                confetti,
            ],
            alignment=ft.alignment.center,
        )
    )


ft.app(main)
