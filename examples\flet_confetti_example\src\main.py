import flet as ft
import math
from flet_confetti import Flet<PERSON>on<PERSON>tti, BlastDirectionality


def main(page: ft.Page):
    page.title = "Flet Confetti Example - Enhanced"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create confetti with enhanced configuration
    confetti = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        colors=["green", "blue", "pink", "orange", "purple"],
        create_particle_path="star",
        duration_seconds=5,  # 5 second duration
        number_of_particles=50,
        should_loop=False,
        width=400,
        height=300,
    )

    # Status text to show results
    status_text = ft.Text(
        "Ready to blast confetti!",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
    )

    def on_animation_end(e):
        status_text.value = "Animation ended!"
        page.update()

    def on_play_click(e):
        result = confetti.play()
        status_text.value = f"Play result: {result}"
        page.update()

    def on_stop_click(e):
        result = confetti.stop(clear_all_particles=False)
        status_text.value = f"Stop result: {result}"
        page.update()

    def on_stop_clear_click(e):
        result = confetti.stop(clear_all_particles=True)
        status_text.value = f"Stop with clear result: {result}"
        page.update()

    def on_reload_click(e):
        result = confetti.reload()
        status_text.value = f"Reload result: {result}"
        page.update()

    # Set event handler
    confetti.on_animation_end = on_animation_end

    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER

    page.add(
        confetti,
        status_text,
        ft.Row(
            [
                ft.ElevatedButton(
                    "🎉 Play",
                    on_click=on_play_click,
                    bgcolor=ft.Colors.GREEN,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "⏹️ Stop",
                    on_click=on_stop_click,
                    bgcolor=ft.Colors.ORANGE,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "🧹 Stop & Clear",
                    on_click=on_stop_clear_click,
                    bgcolor=ft.Colors.RED,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "🔄 Reload",
                    on_click=on_reload_click,
                    bgcolor=ft.Colors.BLUE,
                    color=ft.Colors.WHITE,
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=10,
        ),
    )


ft.app(main)
