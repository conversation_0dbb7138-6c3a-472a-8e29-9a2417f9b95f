import flet as ft
import math
import random
from flet_confetti import FletConfetti, BlastDirectionality, ParticleShape, ConfettiTheme
from flet.core.size import Size


def main(page: ft.Page):
    page.title = "Flet Confetti - Visual Properties & Directional Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create main confetti (center - debug mode) using Size and theme
    confetti_center = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        theme=ConfettiTheme.BIRTHDAY,  # Use theme instead of colors
        particle_shape=ParticleShape.HEART,
        duration_seconds=5,
        number_of_particles=10,
        should_loop=False,
        max_blast_force=40,
        min_blast_force=20,
        gravity=0.4,
        minimum_size=Size(15, 8),  # Use Size instead of individual width/height
        maximum_size=Size(25, 12),
    )

    # Create directional confettis
    confetti_top = FletConfetti(
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=math.pi / 2,  # Down
        theme=ConfettiTheme.CHRISTMAS,  # Red, green, gold theme
        particle_shape=ParticleShape.STAR,
        duration_seconds=3,
        number_of_particles=15,
        max_blast_force=30,
        min_blast_force=15,
        gravity=0.3,
        minimum_size=Size(12, 6),
        maximum_size=Size(20, 10),
        top=0
    )

    confetti_bottom = FletConfetti(
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=-math.pi / 2,  # Up
        theme=ConfettiTheme.OCEAN,  # Blue, teal, aqua theme
        particle_shape=ParticleShape.TRIANGLE,
        duration_seconds=3,
        number_of_particles=15,
        max_blast_force=30,
        min_blast_force=15,
        gravity=-0.3,  # Negative gravity for upward
        minimum_size=Size(10, 8),
        maximum_size=Size(18, 12),
        bottom=0,
    )

    confetti_left = FletConfetti(
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=0,  # Right
        theme=ConfettiTheme.FOREST,  # Green nature theme
        particle_shape=ParticleShape.DIAMOND,
        duration_seconds=3,
        number_of_particles=15,
        max_blast_force=30,
        min_blast_force=15,
        gravity=0.2,
        minimum_size=Size(14, 7),
        maximum_size=Size(22, 11),
        left=0,
    )

    confetti_right = FletConfetti(
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=math.pi,  # Left
        theme=ConfettiTheme.SUNSET,  # Orange, pink, purple theme
        particle_shape=ParticleShape.FLOWER,
        duration_seconds=3,
        number_of_particles=15,
        max_blast_force=30,
        min_blast_force=15,
        gravity=0.2,
        minimum_size=Size(16, 9),
        maximum_size=Size(24, 13),
        right=0,
    )

    # Create property display texts that will update in real-time
    prop_shape = ft.Text("Shape: heart", size=12, color=ft.Colors.YELLOW, weight=ft.FontWeight.W_500)
    prop_directionality = ft.Text("Directionality: explosive", size=12, color=ft.Colors.CYAN, weight=ft.FontWeight.W_500)
    prop_direction = ft.Text("Direction: 0.00", size=12, color=ft.Colors.GREEN, weight=ft.FontWeight.W_500)
    prop_particles = ft.Text("Particles: 10", size=12, color=ft.Colors.ORANGE, weight=ft.FontWeight.W_500)
    prop_max_force = ft.Text("Max Force: 40.0", size=12, color=ft.Colors.RED, weight=ft.FontWeight.W_500)
    prop_min_force = ft.Text("Min Force: 20.0", size=12, color=ft.Colors.PINK, weight=ft.FontWeight.W_500)
    prop_gravity = ft.Text("Gravity: 0.40", size=12, color=ft.Colors.PURPLE, weight=ft.FontWeight.W_500)
    prop_emission = ft.Text("Emission: 0.050", size=12, color=ft.Colors.BLUE, weight=ft.FontWeight.W_500)
    prop_duration = ft.Text("Duration: 5s", size=12, color=ft.Colors.LIME, weight=ft.FontWeight.W_500)
    prop_theme = ft.Text("Theme: birthday", size=12, color=ft.Colors.DEEP_ORANGE, weight=ft.FontWeight.W_500)
    prop_size = ft.Text("Size: 15x8 to 25x12", size=12, color=ft.Colors.TEAL, weight=ft.FontWeight.W_500)
    prop_colors = ft.Text("Colors: 6 colors", size=12, color=ft.Colors.AMBER, weight=ft.FontWeight.W_500)

    # Function to update all property displays
    def update_properties():
        prop_shape.value = f"Shape: {confetti_center.particle_shape.value if confetti_center.particle_shape else 'None'}"
        prop_directionality.value = f"Directionality: {confetti_center.blast_directionality.value}"
        prop_direction.value = f"Direction: {confetti_center.blast_direction:.2f}"
        prop_particles.value = f"Particles: {confetti_center.number_of_particles}"
        prop_max_force.value = f"Max Force: {confetti_center.max_blast_force:.1f}"
        prop_min_force.value = f"Min Force: {confetti_center.min_blast_force:.1f}"
        prop_gravity.value = f"Gravity: {confetti_center.gravity:.2f}"
        prop_emission.value = f"Emission: {confetti_center.emission_frequency:.3f}"
        prop_duration.value = f"Duration: {confetti_center.duration_seconds}s"

        # Update theme display
        try:
            if confetti_center.theme:
                prop_theme.value = f"Theme: {confetti_center.theme.value}"
            else:
                prop_theme.value = "Theme: custom colors"
        except:
            prop_theme.value = "Theme: unknown"

        # Update size display
        try:
            if confetti_center.minimum_size and confetti_center.maximum_size:
                min_size = confetti_center.minimum_size
                max_size = confetti_center.maximum_size
                prop_size.value = f"Size: {min_size.width:.0f}x{min_size.height:.0f} to {max_size.width:.0f}x{max_size.height:.0f}"
            else:
                prop_size.value = f"Size: {confetti_center.minimum_size_width:.0f}x{confetti_center.minimum_size_height:.0f} to {confetti_center.maximum_size_width:.0f}x{confetti_center.maximum_size_height:.0f}"
        except:
            prop_size.value = "Size: unknown"

        # Update colors count
        try:
            if confetti_center.colors:
                prop_colors.value = f"Colors: {len(confetti_center.colors)} colors"
            else:
                prop_colors.value = "Colors: theme colors"
        except:
            prop_colors.value = "Colors: unknown"

        page.update()

    # Status text
    status_text = ft.Text(
        "🎊 Ready to blast confetti! 🎊",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )

    # Event handlers
    def on_animation_end(_):
        status_text.value = "✨ Animation ended!"
        page.update()

    def randomize_center_confetti():
        """Randomize center confetti properties and update display"""
        try:
            # Randomize theme (70% chance) or colors (30% chance)
            if random.random() < 0.7:
                # Use random theme
                themes = [
                    ConfettiTheme.CHRISTMAS, ConfettiTheme.HALLOWEEN, ConfettiTheme.VALENTINE,
                    ConfettiTheme.EASTER, ConfettiTheme.NEW_YEAR, ConfettiTheme.GOLD,
                    ConfettiTheme.SILVER, ConfettiTheme.BIRTHDAY, ConfettiTheme.NEON,
                    ConfettiTheme.PASTEL, ConfettiTheme.OCEAN, ConfettiTheme.SUNSET,
                    ConfettiTheme.FOREST, ConfettiTheme.PRIDE, ConfettiTheme.MARDI_GRAS
                ]
                selected_theme = random.choice(themes)
                confetti_center.theme = selected_theme
                print(f"Selected theme: {selected_theme.value}")
            else:
                # Use random colors
                random_colors = [ft.Colors.random() for _ in range(random.randint(3, 6))]
                confetti_center.colors = random_colors
                print(f"Selected {len(random_colors)} random colors")

            # Randomize size
            min_width = random.uniform(10, 20)
            min_height = random.uniform(6, 12)
            max_width = random.uniform(min_width + 5, min_width + 15)
            max_height = random.uniform(min_height + 3, min_height + 10)
            confetti_center.minimum_size = Size(min_width, min_height)
            confetti_center.maximum_size = Size(max_width, max_height)
        except Exception as e:
            print(f"Error in randomize_center_confetti: {e}")
            # Fallback to simple randomization
            confetti_center.colors = ["red", "blue", "green", "orange"]

        # Randomize shape
        shapes = [
            ParticleShape.RECTANGLE,
ParticleShape.CIRCLE,
ParticleShape.SQUARE,
ParticleShape.TRIANGLE,
ParticleShape.DIAMOND,
ParticleShape.HEXAGON,
ParticleShape.PENTAGON,
ParticleShape.OCTAGON,
ParticleShape.STAR,
ParticleShape.STAR_4,
ParticleShape.STAR_6,
ParticleShape.STAR_8,
ParticleShape.HEART,
ParticleShape.FLOWER,
ParticleShape.LEAF,
ParticleShape.BUTTERFLY,
ParticleShape.CROSS,
ParticleShape.PLUS,
ParticleShape.ARROW,
ParticleShape.LIGHTNING,
        ]
        confetti_center.particle_shape = random.choice(shapes)
        
        # Randomize other properties
        if random.random() < 0.5:
            confetti_center.blast_directionality = BlastDirectionality.DIRECTIONAL
        else:
            confetti_center.blast_directionality = BlastDirectionality.EXPLOSIVE
        
        confetti_center.blast_direction = random.uniform(0, 2 * math.pi)
        confetti_center.max_blast_force = random.uniform(20, 80)
        confetti_center.min_blast_force = random.uniform(10, 40)
        confetti_center.emission_frequency = random.uniform(0.01, 0.09)
        confetti_center.gravity = random.uniform(-0.6, 0.6)
        confetti_center.number_of_particles = random.randint(5, 20)
        
        # Update property display
        update_properties()

    def on_center_play(_):
        """Play center confetti with randomized properties"""
        try:
            randomize_center_confetti()
            confetti_center.reload()
            result = confetti_center.play()
            status_text.value = f"🎊 Center: {confetti_center.particle_shape.value} confetti!"
            print(f"Center confetti: {result}")
        except Exception as e:
            status_text.value = f"❌ Center error: {e}"
        page.update()

    def on_top_play(_):
        """Play top confetti"""
        try:
            confetti_top.play()
            status_text.value = "⬇️ Top confetti blasting down!"
        except Exception as e:
            status_text.value = f"❌ Top error: {e}"
        page.update()

    def on_bottom_play(_):
        """Play bottom confetti"""
        try:
            confetti_bottom.play()
            status_text.value = "⬆️ Bottom confetti blasting up!"
        except Exception as e:
            status_text.value = f"❌ Bottom error: {e}"
        page.update()

    def on_left_play(_):
        """Play left confetti"""
        try:
            confetti_left.play()
            status_text.value = "➡️ Left confetti blasting right!"
        except Exception as e:
            status_text.value = f"❌ Left error: {e}"
        page.update()

    def on_right_play(_):
        """Play right confetti"""
        try:
            confetti_right.play()
            status_text.value = "⬅️ Right confetti blasting left!"
        except Exception as e:
            status_text.value = f"❌ Right error: {e}"
        page.update()

    def stop_all(_):
        """Stop all confetti"""
        try:
            confetti_center.stop(clear_all_particles=True)
            confetti_top.stop(clear_all_particles=True)
            confetti_bottom.stop(clear_all_particles=True)
            confetti_left.stop(clear_all_particles=True)
            confetti_right.stop(clear_all_particles=True)
            status_text.value = "🛑 All confetti stopped!"
        except Exception as e:
            status_text.value = f"❌ Stop error: {e}"
        page.update()

    # Set event handlers
    confetti_center.on_animation_end = on_animation_end
    
    # Initialize property display
    update_properties()

    # Create the main layout using Column and Row structure
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                # Main layout structure
                ft.Column(
                    [
                        # Top row with top button
                        ft.Container(
                            content=ft.ElevatedButton(
                                "⬇️ TOP",
                                on_click=on_top_play,
                                bgcolor=ft.Colors.RED_400,
                                color=ft.Colors.WHITE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=20),
                                    padding=ft.Padding(20, 10, 20, 10),
                                ),
                            ),
                            alignment=ft.alignment.center,
                            padding=ft.Padding(0, 10, 0, 5),
                        ),
                        
                        # Middle row with left, center, right
                        ft.Row(
                            [
                                # Left button
                                ft.ElevatedButton(
                                    "➡️\nLEFT",
                                    on_click=on_left_play,
                                    bgcolor=ft.Colors.GREEN_400,
                                    color=ft.Colors.WHITE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=20),
                                        padding=ft.Padding(10, 30, 10, 30),
                                    ),
                                ),
                                
                                # Center debug area
                                ft.Container(
                                    content=ft.Column(
                                        [
                                            ft.Text(
                                                "🎊 CONFETTI DEBUG CENTER 🎊",
                                                size=18,
                                                color=ft.Colors.WHITE,
                                                weight=ft.FontWeight.BOLD,
                                                text_align=ft.TextAlign.CENTER,
                                            ),
                                            status_text,
                                            ft.Divider(color=ft.Colors.WHITE24, height=20),
                                            
                                            # Properties display in a grid
                                            ft.Column([
                                                ft.Row([prop_shape, prop_directionality], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                                ft.Row([prop_direction, prop_particles], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                                ft.Row([prop_max_force, prop_min_force], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                                ft.Row([prop_gravity, prop_emission], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                                ft.Row([prop_duration, prop_theme], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                                ft.Row([prop_size, prop_colors], alignment=ft.MainAxisAlignment.CENTER, spacing=20),
                                            ], spacing=8),
                                            
                                            ft.Divider(color=ft.Colors.WHITE24, height=20),
                                            
                                            # Center control buttons
                                            ft.Row([
                                                ft.ElevatedButton(
                                                    "🎉 PLAY CENTER",
                                                    on_click=on_center_play,
                                                    bgcolor=ft.Colors.ORANGE_600,
                                                    color=ft.Colors.WHITE,
                                                    style=ft.ButtonStyle(
                                                        padding=ft.Padding(15, 8, 15, 8),
                                                    ),
                                                ),
                                                ft.ElevatedButton(
                                                    "🛑 STOP ALL",
                                                    on_click=stop_all,
                                                    bgcolor=ft.Colors.RED_600,
                                                    color=ft.Colors.WHITE,
                                                    style=ft.ButtonStyle(
                                                        padding=ft.Padding(15, 8, 15, 8),
                                                    ),
                                                ),
                                            ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                                            
                                            ft.Text(
                                                "💡 Properties update in real-time!",
                                                size=12,
                                                color=ft.Colors.WHITE70,
                                                text_align=ft.TextAlign.CENTER,
                                                italic=True,
                                            ),
                                        ],
                                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                        spacing=10,
                                    ),
                                    bgcolor=ft.Colors.with_opacity(0.8, ft.Colors.BLACK),
                                    border_radius=15,
                                    padding=20,
                                    expand=True,
                                ),
                                
                                # Right button
                                ft.ElevatedButton(
                                    "⬅️\nRIGHT",
                                    on_click=on_right_play,
                                    bgcolor=ft.Colors.PURPLE_400,
                                    color=ft.Colors.WHITE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=20),
                                        padding=ft.Padding(10, 30, 10, 30),
                                    ),
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            expand=True,
                            spacing=20,
                        ),
                        
                        # Bottom row with bottom button
                        ft.Container(
                            content=ft.ElevatedButton(
                                "⬆️ BOTTOM",
                                on_click=on_bottom_play,
                                bgcolor=ft.Colors.BLUE_400,
                                color=ft.Colors.WHITE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=20),
                                    padding=ft.Padding(20, 10, 20, 10),
                                ),
                            ),
                            alignment=ft.alignment.center,
                            padding=ft.Padding(0, 5, 0, 10),
                        ),
                    ],
                    expand=True,
                    spacing=0,
                ),
                
                # All confetti widgets (positioned for effects)
                confetti_top,
                confetti_bottom,
                confetti_left,
                confetti_right,
                confetti_center,
            ],
            alignment=ft.alignment.center,
        )
    )


if __name__ == "__main__":
    ft.app(main)
