import flet as ft
import math
from flet_confetti import FletConfetti, BlastDirectionality


def main(page: ft.Page):
    page.title = "Flet Confetti Example"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900
    confetti = FletConfetti(blast_directionality=BlastDirectionality.EXPLOSIVE)
    page.add(confetti)
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.add(
        ft.ElevatedButton(
            "Play",
            on_click=lambda e: confetti.play(),
            bgcolor=ft.Colors.PURPLE,
            color=ft.Colors.WHITE,
        ),
        ft.ElevatedButton(
            "Stop",
            on_click=lambda e: confetti.stop(),
            bgcolor=ft.Colors.RED,
            color=ft.Colors.WHITE,
        ),
    )


ft.app(main)
