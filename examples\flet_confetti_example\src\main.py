import flet as ft
import math
from flet_confetti import Flet<PERSON>on<PERSON>tti, BlastDirectionality


def main(page: ft.Page):
    page.title = "Flet Confetti Example - Enhanced"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20
    page.bgcolor = ft.Colors.GREY_900

    # Create confetti with enhanced configuration
    confetti = FletConfetti(
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        colors=["green", "blue", "pink", "orange", "purple"],
        create_particle_path="star",
        duration_seconds=5,  # 5 second duration
        number_of_particles=50,
        should_loop=False,
    )

    # Status text to show results
    status_text = ft.Text(
        "Ready to blast confetti!",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
    )

    def on_animation_end(_):
        status_text.value = "Animation ended!"
        page.update()

    def on_play_click(_):
        try:
            result = confetti.play()
            status_text.value = f"✅ Play result: {result}"
            print(f"Play called, result: {result}")
        except Exception as e:
            status_text.value = f"❌ Play error: {e}"
            print(f"Play error: {e}")
        page.update()

    def on_stop_click(_):
        try:
            result = confetti.stop(clear_all_particles=False)
            status_text.value = f"⏹️ Stop result: {result}"
            print(f"Stop called, result: {result}")
        except Exception as e:
            status_text.value = f"❌ Stop error: {e}"
            print(f"Stop error: {e}")
        page.update()

    def on_stop_clear_click(_):
        try:
            result = confetti.stop(clear_all_particles=True)
            status_text.value = f"🧹 Stop+Clear result: {result}"
            print(f"Stop with clear called, result: {result}")
        except Exception as e:
            status_text.value = f"❌ Stop+Clear error: {e}"
            print(f"Stop+Clear error: {e}")
        page.update()

    def on_reload_click(_):
        try:
            result = confetti.reload()
            status_text.value = f"🔄 Reload result: {result}"
            print(f"Reload called, result: {result}")
            # Small delay to ensure reload is complete
            import time

            time.sleep(0.1)
            status_text.value += " - Ready for new animations!"
        except Exception as e:
            status_text.value = f"❌ Reload error: {e}"
            print(f"Reload error: {e}")
        page.update()

    def on_check_state_click(_):
        try:
            state = confetti.get_controller_state()
            status_text.value = f"🔍 Controller state: {state}"
            print(f"Controller state: {state}")
        except Exception as e:
            status_text.value = f"❌ State check error: {e}"
            print(f"State check error: {e}")
        page.update()

    # Set event handler
    confetti.on_animation_end = on_animation_end

    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER

    page.add(
        confetti,
        status_text,
        ft.Row(
            [
                ft.ElevatedButton(
                    "🎉 Play",
                    on_click=on_play_click,
                    bgcolor=ft.Colors.GREEN,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "⏹️ Stop",
                    on_click=on_stop_click,
                    bgcolor=ft.Colors.ORANGE,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "🧹 Stop & Clear",
                    on_click=on_stop_clear_click,
                    bgcolor=ft.Colors.RED,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "🔄 Reload",
                    on_click=on_reload_click,
                    bgcolor=ft.Colors.BLUE,
                    color=ft.Colors.WHITE,
                ),
                ft.ElevatedButton(
                    "🔍 Check State",
                    on_click=on_check_state_click,
                    bgcolor=ft.Colors.PURPLE,
                    color=ft.Colors.WHITE,
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=10,
        ),
    )


ft.app(main)
